import dotenv from "dotenv";
import { Langfuse } from "langfuse";
import { callLLM } from "../utils/callLLM.js";

dotenv.config();

const langfuse = new Langfuse({
  secretKey: process.env.LANGFUSE_SECRET_KEY,
  publicKey: process.env.LANGFUSE_PUBLIC_KEY,
  baseUrl: process.env.LANGFUSE_BASE_URL,
});

// Interface for studio content generation parameters
export interface StudioContentGenerationParams {
  taskDescription: string;
  selectedCompanyContent: any;
  selectedPersonas: string[];
  personas: any[];
  selectedIcps: string[];
  icps: any[];
  selectedResearch: string[];
  researchItems: any[];
  selectedDocuments: any[];
  seoKeywords: string[];
  trendKeywords: string[];
  companyBrand: any;
}

export const generateContentBody = async (
  channel: string,
  content_type: string,
  task_description: string,
  personas_data: any[] | string,
  icps_data: any[] | string,
  research_data: any[] | string,
  documents_data: any[] | string,
  seo_keywords: string,
  trend_keywords: string,
  brand_guidelines: string | object
): Promise<string> => {
  try {
    // Get production prompt
    const prompt: any = await langfuse.getPrompt(
      "generate_content_body_v2",
      undefined,
      { label: "production" }
    );
    console.log("Full prompt object:", JSON.stringify(prompt, null, 2));
    console.log("prompt.config exists:", !!prompt.config);
    console.log("prompt.config:", prompt.config);

    // Pass structured data directly to Langfuse for templating
    const compiledMessages = prompt.compile({
      channel: channel,
      content_type: content_type,
      task_description: task_description,
      personas_block: personas_data,
      icps_block: icps_data,
      research_block: research_data,
      documents_block: documents_data,
      seo_keywords: seo_keywords,
      trend_keywords: trend_keywords,
      brand_guidelines: brand_guidelines,
    });
    console.log("prompt object:", prompt);
    console.log("compiledMessages:", compiledMessages);

    // For chat prompts, pass empty string as prompt and use additionalMessages
    const response = await callLLM(
      "",
      prompt.config, // Use the config portion of the prompt
      "google/gemini-2.5-pro-preview",
      { parse: false },
      compiledMessages
    );
    return response;
  } catch (error) {
    console.error("Error generating content body:", error);
    throw error;
  }
};

/**
 * Helper functions to transform frontend data for Langfuse prompt compilation
 * These functions prepare structured data that Langfuse can template properly
 */
function preparePersonasData(selectedPersonas: string[], personas: any[]): any[] {
  if (selectedPersonas.length === 0) return [];

  return personas
    .filter((p) => selectedPersonas.includes(p.id))
    .map((p) => {
      const personaData = p.data && typeof p.data === 'object' ? p.data : {};
      return {
        name: p.name,
        description: (personaData as any)?.data || 'Target audience segment'
      };
    });
}

function prepareIcpsData(selectedIcps: string[], icps: any[]): any[] {
  if (selectedIcps.length === 0) return [];

  return icps
    .filter((i) => selectedIcps.includes(i.id))
    .map((i) => {
      const icpData = i.data && typeof i.data === 'object' ? i.data : {};
      return {
        name: i.name,
        description: (icpData as any).data || 'Ideal customer profile'
      };
    });
}

function prepareResearchData(selectedResearch: string[], researchItems: any[]): any[] {
  if (selectedResearch.length === 0) return [];

  return researchItems
    .filter((r) => selectedResearch.includes(r.id))
    .map((r) => {
      const researchData = r.data && typeof r.data === 'object' ? r.data : {};
      return {
        title: r.name,
        description: (researchData as any).description || (researchData as any).summary || 'External research insight',
        content: (researchData as any).source_content || ''
      };
    });
}

function prepareDocumentsData(selectedDocuments: any[]): any[] {
  if (selectedDocuments.length === 0) return [];

  return selectedDocuments.map((doc) => ({
    title: doc.documentTitle,
    content: doc.content.substring(0, 1000) + (doc.content.length > 1000 ? '...' : '')
  }));
}

/**
 * Studio content generation function that maps frontend parameters to server format
 */
export const generateStudioContentBody = async (
  params: StudioContentGenerationParams
): Promise<string> => {
  const {
    taskDescription,
    selectedCompanyContent,
    selectedPersonas,
    personas,
    selectedIcps,
    icps,
    selectedResearch,
    researchItems,
    selectedDocuments,
    seoKeywords,
    trendKeywords,
    companyBrand,
  } = params;

  // Extract channel and content type from selectedCompanyContent
  const channel = selectedCompanyContent?.channel || 'Not specified';
  const content_type = selectedCompanyContent?.content_type || 'Not specified';

  // Prepare structured data for Langfuse prompt compilation
  const personas_data = preparePersonasData(selectedPersonas, personas);
  const icps_data = prepareIcpsData(selectedIcps, icps);
  const research_data = prepareResearchData(selectedResearch, researchItems);
  const documents_data = prepareDocumentsData(selectedDocuments);
  const seo_keywords = seoKeywords.join(', ');
  const trend_keywords = trendKeywords.join(', ');
  const brand_guidelines = companyBrand;

  // Call the updated generateContentBody function with structured data
  return await generateContentBody(
    channel,
    content_type,
    taskDescription,
    personas_data,
    icps_data,
    research_data,
    documents_data,
    seo_keywords,
    trend_keywords,
    brand_guidelines
  );
};

/**
 * Get compiled Langfuse prompt for studio content generation (without calling LLM)
 * This is used for the hybrid streaming approach
 */
export const getStudioContentPrompt = async (
  params: StudioContentGenerationParams
): Promise<{ compiledPrompt: any; config: any }> => {
  const {
    taskDescription,
    selectedCompanyContent,
    selectedPersonas,
    personas,
    selectedIcps,
    icps,
    selectedResearch,
    researchItems,
    selectedDocuments,
    seoKeywords,
    trendKeywords,
    companyBrand,
  } = params;

  // Extract channel and content type from selectedCompanyContent
  const channel = selectedCompanyContent?.channel || 'Not specified';
  const content_type = selectedCompanyContent?.content_type || 'Not specified';

  // Prepare structured data for Langfuse prompt compilation
  const personas_data = preparePersonasData(selectedPersonas, personas);
  const icps_data = prepareIcpsData(selectedIcps, icps);
  const research_data = prepareResearchData(selectedResearch, researchItems);
  const documents_data = prepareDocumentsData(selectedDocuments);
  const seo_keywords = seoKeywords.join(', ');
  const trend_keywords = trendKeywords.join(', ');
  const brand_guidelines = companyBrand;

  try {
    // Get production prompt from Langfuse
    const prompt: any = await langfuse.getPrompt(
      "generate_content_body_v2",
      undefined,
      { label: "production" }
    );

    // Prepare compilation parameters with structured data
    const compilationParams = {
      channel: channel,
      content_type: content_type,
      task_description: taskDescription,
      personas_block: personas_data,
      icps_block: icps_data,
      research_block: research_data,
      documents_block: documents_data,
      seo_keywords: seo_keywords,
      trend_keywords: trend_keywords,
      brand_guidelines: brand_guidelines,
    };
    // Compile the prompt with parameters
    const compiledMessages = prompt.compile(compilationParams);

    return {
      compiledPrompt: compiledMessages,
      config: prompt.config
    };
  } catch (error) {
    console.error("❌ [ERROR] Error getting studio content prompt:", error);
    throw error;
  }
};
