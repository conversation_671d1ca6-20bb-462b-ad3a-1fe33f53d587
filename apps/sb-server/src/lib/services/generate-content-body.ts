import dotenv from "dotenv";
import { Langfuse } from "langfuse";
import { callLLM } from "../utils/callLLM.js";

dotenv.config();

const langfuse = new Langfuse({
  secretKey: process.env.LANGFUSE_SECRET_KEY,
  publicKey: process.env.LANGFUSE_PUBLIC_KEY,
  baseUrl: process.env.LANGFUSE_BASE_URL,
});

// Interface for studio content generation parameters
export interface StudioContentGenerationParams {
  taskDescription: string;
  selectedCompanyContent: any;
  selectedPersonas: string[];
  personas: any[];
  selectedIcps: string[];
  icps: any[];
  selectedResearch: string[];
  researchItems: any[];
  selectedDocuments: any[];
  seoKeywords: string[];
  trendKeywords: string[];
  companyBrand: any;
}

export const generateContentBody = async (
  channel: string,
  content_type: string,
  task_description: string,
  personas_block: string | object,
  icps_block: string | object,
  research_block: string | object,
  documents_block: string | object,
  seo_keywords: string,
  trend_keywords: string,
  brand_guidelines: string | object
): Promise<string> => {
  console.log(
    "channel",
    channel,
    "content_type",
    content_type,
    "task_description",
    task_description,
    "personas_block",
    personas_block,
    "icps_block",
    icps_block,
    "research_block",
    research_block,
    "documents_block",
    documents_block,
    "seo_keywords",
    seo_keywords,
    "trend_keywords",
    trend_keywords,
    "brand_guidelines",
    brand_guidelines
  );
  try {
    // Get production prompt
    const prompt: any = await langfuse.getPrompt(
      "generate_content_body_v2",
      undefined,
      { label: "production" }
    );
    console.log("Full prompt object:", JSON.stringify(prompt, null, 2));
    console.log("prompt.config exists:", !!prompt.config);
    console.log("prompt.config:", prompt.config);

    // Helper function to stringify objects
    const stringify = (value: any): string => {
      if (typeof value === "object" && value !== null) {
        return JSON.stringify(value, null, 2);
      }
      return String(value);
    };

    const compiledMessages = prompt.compile({
      channel: channel,
      content_type: content_type,
      task_description: task_description,
      personas_block: stringify(personas_block),
      icps_block: stringify(icps_block),
      research_block: stringify(research_block),
      documents_block: stringify(documents_block),
      seo_keywords: seo_keywords,
      trend_keywords: trend_keywords,
      brand_guidelines: stringify(brand_guidelines),
    });
    console.log("prompt object:", prompt);
    console.log("compiledMessages:", compiledMessages);

    // For chat prompts, pass empty string as prompt and use additionalMessages
    const response = await callLLM(
      "",
      prompt.config, // Use the config portion of the prompt
      "google/gemini-2.5-pro-preview",
      { parse: false },
      compiledMessages
    );
    return response;
  } catch (error) {
    console.error("Error generating content body:", error);
    throw error;
  }
};

/**
 * Helper functions to transform frontend data to server format
 */
function transformPersonasBlock(selectedPersonas: string[], personas: any[]): string {
  if (selectedPersonas.length === 0) return "";

  const personasSection = personas
    .filter((p) => selectedPersonas.includes(p.id))
    .map((p) => {
      const personaData = p.data && typeof p.data === 'object' ? p.data : {};
      return `<Persona name="${p.name}">\n<Description>${JSON.stringify((personaData as any)?.data) || 'Target audience segment'}</Description>\n</Persona>`;
    })
    .join('\n');

  return `<Personas>\n${personasSection}\n</Personas>`;
}

function transformIcpsBlock(selectedIcps: string[], icps: any[]): string {
  if (selectedIcps.length === 0) return "";

  const icpsSection = icps
    .filter((i) => selectedIcps.includes(i.id))
    .map((i) => {
      const icpData = i.data && typeof i.data === 'object' ? i.data : {};
      return `<ICP name="${i.name}">\n<Description>${JSON.stringify((icpData as any).data) || 'Ideal customer profile'}</Description>\n</ICP>`;
    })
    .join('\n');

  return `<IdealCustomerProfiles>\n${icpsSection}\n</IdealCustomerProfiles>`;
}

function transformResearchBlock(selectedResearch: string[], researchItems: any[]): string {
  if (selectedResearch.length === 0) return "<Message>No third-party research was provided.</Message>";

  const researchContent = researchItems
    .filter((r) => selectedResearch.includes(r.id))
    .map((r, index) => {
      const researchData = r.data && typeof r.data === 'object' ? r.data : {};
      return `<research_article_${index + 1}>
        <title>${r.name}</title>
        <description>${(researchData as any).description || (researchData as any).summary || 'External research insight'}</description>
        <full_content>${(researchData as any).source_content || ''}</full_content>
        </research_article_${index + 1}>`;
    })
    .join('\n\n');

  return researchContent;
}

function transformDocumentsBlock(selectedDocuments: any[]): string {
  if (selectedDocuments.length === 0) return "<Message>No company documents were provided.</Message>";

  const documentsContent = selectedDocuments
    .map((doc) =>
      `<Document title="${doc.documentTitle}">\n${doc.content.substring(0, 1000)}${doc.content.length > 1000 ? '...' : ''}\n</Document>`
    )
    .join('\n\n');

  return documentsContent;
}

/**
 * Studio content generation function that maps frontend parameters to server format
 */
export const generateStudioContentBody = async (
  params: StudioContentGenerationParams
): Promise<string> => {
  const {
    taskDescription,
    selectedCompanyContent,
    selectedPersonas,
    personas,
    selectedIcps,
    icps,
    selectedResearch,
    researchItems,
    selectedDocuments,
    seoKeywords,
    trendKeywords,
    companyBrand,
  } = params;

  // Extract channel and content type from selectedCompanyContent
  const channel = selectedCompanyContent?.channel || 'Not specified';
  const content_type = selectedCompanyContent?.content_type || 'Not specified';

  // Transform frontend data to server format
  const personas_block = transformPersonasBlock(selectedPersonas, personas);
  const icps_block = transformIcpsBlock(selectedIcps, icps);
  const research_block = transformResearchBlock(selectedResearch, researchItems);
  const documents_block = transformDocumentsBlock(selectedDocuments);
  const seo_keywords = seoKeywords.join(', ');
  const trend_keywords = trendKeywords.join(', ');
  const brand_guidelines = companyBrand;

  // Call the existing generateContentBody function
  return await generateContentBody(
    channel,
    content_type,
    taskDescription,
    personas_block,
    icps_block,
    research_block,
    documents_block,
    seo_keywords,
    trend_keywords,
    brand_guidelines
  );
};

/**
 * Get compiled Langfuse prompt for studio content generation (without calling LLM)
 * This is used for the hybrid streaming approach
 */
export const getStudioContentPrompt = async (
  params: StudioContentGenerationParams
): Promise<{ compiledPrompt: any; config: any }> => {
  console.log('🔍 [DEBUG] getStudioContentPrompt called with params:', {
    taskDescription: params.taskDescription?.substring(0, 100) + '...',
    selectedCompanyContent: params.selectedCompanyContent,
    selectedPersonas: params.selectedPersonas,
    personas: params.personas?.length,
    selectedIcps: params.selectedIcps,
    icps: params.icps?.length,
    selectedResearch: params.selectedResearch,
    researchItems: params.researchItems?.length,
    selectedDocuments: params.selectedDocuments?.length,
    seoKeywords: params.seoKeywords,
    trendKeywords: params.trendKeywords,
    companyBrand: params.companyBrand ? 'Present' : 'Missing'
  });

  const {
    taskDescription,
    selectedCompanyContent,
    selectedPersonas,
    personas,
    selectedIcps,
    icps,
    selectedResearch,
    researchItems,
    selectedDocuments,
    seoKeywords,
    trendKeywords,
    companyBrand,
  } = params;

  // Extract channel and content type from selectedCompanyContent
  const channel = selectedCompanyContent?.channel || 'Not specified';
  const content_type = selectedCompanyContent?.content_type || 'Not specified';

  console.log('🔍 [DEBUG] Extracted channel and content_type:', { channel, content_type });

  // Transform frontend data to server format
  const personas_block = transformPersonasBlock(selectedPersonas, personas);
  const icps_block = transformIcpsBlock(selectedIcps, icps);
  const research_block = transformResearchBlock(selectedResearch, researchItems);
  const documents_block = transformDocumentsBlock(selectedDocuments);
  const seo_keywords = seoKeywords.join(', ');
  const trend_keywords = trendKeywords.join(', ');
  const brand_guidelines = companyBrand;

  console.log('🔍 [DEBUG] Transformed data blocks:', {
    personas_block: personas_block.substring(0, 200) + '...',
    icps_block: icps_block.substring(0, 200) + '...',
    research_block: research_block.substring(0, 200) + '...',
    documents_block: documents_block.substring(0, 200) + '...',
    seo_keywords,
    trend_keywords,
    brand_guidelines: brand_guidelines ? 'Present' : 'Missing'
  });

  try {
    // Get production prompt from Langfuse
    console.log('🔍 [DEBUG] Fetching Langfuse prompt: generate_content_body_v2');
    const prompt: any = await langfuse.getPrompt(
      "generate_content_body_v2",
      undefined,
      { label: "production" }
    );

    console.log("🔍 [DEBUG] Retrieved Langfuse prompt structure:", {
      hasPrompt: !!prompt,
      hasConfig: !!prompt?.config,
      promptType: typeof prompt,
      configType: typeof prompt?.config
    });

    // Helper function to stringify objects
    const stringify = (value: any): string => {
      if (typeof value === "object" && value !== null) {
        return JSON.stringify(value, null, 2);
      }
      return String(value);
    };

    // Prepare compilation parameters
    const compilationParams = {
      channel: channel,
      content_type: content_type,
      task_description: taskDescription,
      personas_block: stringify(personas_block),
      icps_block: stringify(icps_block),
      research_block: stringify(research_block),
      documents_block: stringify(documents_block),
      seo_keywords: seo_keywords,
      trend_keywords: trend_keywords,
      brand_guidelines: stringify(brand_guidelines),
    };

    console.log('🔍 [DEBUG] Compilation parameters:', {
      channel,
      content_type,
      task_description: taskDescription?.substring(0, 100) + '...',
      personas_block_length: personas_block.length,
      icps_block_length: icps_block.length,
      research_block_length: research_block.length,
      documents_block_length: documents_block.length,
      seo_keywords,
      trend_keywords,
      brand_guidelines_present: !!brand_guidelines
    });

    // Compile the prompt with parameters
    console.log('🔍 [DEBUG] Compiling prompt with parameters...');
    const compiledMessages = prompt.compile(compilationParams);

    console.log("🔍 [DEBUG] Compiled prompt result:", {
      type: typeof compiledMessages,
      isArray: Array.isArray(compiledMessages),
      length: Array.isArray(compiledMessages) ? compiledMessages.length : 'N/A',
      firstMessage: Array.isArray(compiledMessages) && compiledMessages.length > 0
        ? compiledMessages[0]
        : compiledMessages
    });

    console.log("🔍 [DEBUG] Full compiled messages (first 500 chars):",
      JSON.stringify(compiledMessages).substring(0, 500) + '...'
    );

    return {
      compiledPrompt: compiledMessages,
      config: prompt.config
    };
  } catch (error) {
    console.error("❌ [ERROR] Error getting studio content prompt:", error);
    throw error;
  }
};
