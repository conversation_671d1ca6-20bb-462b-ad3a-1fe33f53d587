revoke delete on table "public"."products" from "anon";

revoke insert on table "public"."products" from "anon";

revoke references on table "public"."products" from "anon";

revoke select on table "public"."products" from "anon";

revoke trigger on table "public"."products" from "anon";

revoke truncate on table "public"."products" from "anon";

revoke update on table "public"."products" from "anon";

revoke delete on table "public"."products" from "authenticated";

revoke insert on table "public"."products" from "authenticated";

revoke references on table "public"."products" from "authenticated";

revoke select on table "public"."products" from "authenticated";

revoke trigger on table "public"."products" from "authenticated";

revoke truncate on table "public"."products" from "authenticated";

revoke update on table "public"."products" from "authenticated";

revoke delete on table "public"."products" from "service_role";

revoke insert on table "public"."products" from "service_role";

revoke references on table "public"."products" from "service_role";

revoke select on table "public"."products" from "service_role";

revoke trigger on table "public"."products" from "service_role";

revoke truncate on table "public"."products" from "service_role";

revoke update on table "public"."products" from "service_role";


drop index if exists "zero_0/cvr"."client_patch_version";

drop index if exists "zero_0/cvr"."desires_expires_at";

alter table "zero_0/cvr"."clients" drop column "deleted";

alter table "zero_0/cvr"."clients" drop column "patchVersion";

alter table "zero_0/cvr"."desires" drop column "expiresAt";

alter table "zero_0/cvr"."queries" add column "queryArgs" json;

alter table "zero_0/cvr"."queries" add column "queryName" text;

alter table "zero_0/cvr"."queries" alter column "clientAST" drop not null;


