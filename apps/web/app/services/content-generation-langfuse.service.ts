/**
 * Service for content generation using Langfuse via sb-server
 */
import { SelectedDocument } from '~/components/document-selector';

export interface StudioContentGenerationParams {
  taskDescription: string;
  selectedCompanyContent: any;
  selectedPersonas: string[];
  personas: any[];
  selectedIcps: string[];
  icps: any[];
  selectedResearch: string[];
  researchItems: any[];
  selectedDocuments: SelectedDocument[];
  seoKeywords: string[];
  trendKeywords: string[];
  companyBrand: any;
}

/**
 * Formats personas data for Langfuse prompt
 */
function formatPersonasBlock(selectedPersonas: string[], personas: any[]): string {
  if (selectedPersonas.length === 0) {
    return 'No target personas specified.';
  }

  const personasData = personas
    .filter((p) => selectedPersonas.includes(p.id))
    .map((p) => {
      const personaData = p.data && typeof p.data === 'object' ? p.data : {};
      return {
        name: p.name,
        description: JSON.stringify((personaData as any)?.data) || 'Target audience segment'
      };
    });

  return JSON.stringify(personasData, null, 2);
}

/**
 * Formats ICPs data for Langfuse prompt
 */
function formatIcpsBlock(selectedIcps: string[], icps: any[]): string {
  if (selectedIcps.length === 0) {
    return 'No ideal customer profiles specified.';
  }

  const icpsData = icps
    .filter((i) => selectedIcps.includes(i.id))
    .map((i) => {
      const icpData = i.data && typeof i.data === 'object' ? i.data : {};
      return {
        name: i.name,
        description: JSON.stringify((icpData as any).data) || 'Ideal customer profile'
      };
    });

  return JSON.stringify(icpsData, null, 2);
}

/**
 * Formats research materials for Langfuse prompt
 */
function formatResearchBlock(selectedResearch: string[], researchItems: any[]): string {
  if (selectedResearch.length === 0) {
    return 'No third-party research was provided.';
  }

  const researchData = researchItems
    .filter((r) => selectedResearch.includes(r.id))
    .map((r, index) => {
      const researchData = r.data && typeof r.data === 'object' ? r.data : {};
      return {
        title: r.name,
        description: (researchData as any).description || (researchData as any).summary || 'External research insight',
        content: (researchData as any).source_content || ''
      };
    });

  return JSON.stringify(researchData, null, 2);
}

/**
 * Formats company documents for Langfuse prompt
 */
function formatDocumentsBlock(selectedDocuments: SelectedDocument[]): string {
  if (selectedDocuments.length === 0) {
    return 'No company documents were provided.';
  }

  const documentsData = selectedDocuments.map((doc) => ({
    title: doc.documentTitle,
    content: doc.content.substring(0, 2000) + (doc.content.length > 2000 ? '...' : '')
  }));

  return JSON.stringify(documentsData, null, 2);
}

/**
 * Transforms Studio content generation parameters to Langfuse API format
 */
export function transformStudioParamsToLangfuse(params: StudioContentGenerationParams) {
  const {
    taskDescription,
    selectedCompanyContent,
    selectedPersonas,
    personas,
    selectedIcps,
    icps,
    selectedResearch,
    researchItems,
    selectedDocuments,
    seoKeywords,
    trendKeywords,
    companyBrand,
  } = params;

  return {
    channel: selectedCompanyContent?.channel || 'Not specified',
    content_type: selectedCompanyContent?.content_type || 'Not specified',
    task_description: taskDescription,
    personas_block: formatPersonasBlock(selectedPersonas, personas),
    icps_block: formatIcpsBlock(selectedIcps, icps),
    research_block: formatResearchBlock(selectedResearch, researchItems),
    documents_block: formatDocumentsBlock(selectedDocuments),
    seo_keywords: seoKeywords.join(', '),
    trend_keywords: trendKeywords.join(', '),
    brand_guidelines: JSON.stringify(companyBrand)
  };
}

/**
 * Fetches the compiled Langfuse prompt for use with AI extension
 */
export async function getCompiledPromptFromLangfuse(params: StudioContentGenerationParams): Promise<string> {
  const langfuseParams = transformStudioParamsToLangfuse(params);

  const response = await fetch('/api/ai/get-content-prompt', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(langfuseParams),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error || errorData.message || `Prompt compilation failed: ${response.statusText}`);
  }

  const result = await response.json();
  
  if (!result.success) {
    throw new Error(result.error || result.message || 'Prompt compilation failed');
  }

  return result.prompt;
}

/**
 * Calls the Langfuse-based content generation API (kept for backward compatibility)
 */
export async function generateContentWithLangfuse(params: StudioContentGenerationParams): Promise<string> {
  const langfuseParams = transformStudioParamsToLangfuse(params);

  const response = await fetch('/api/ai/generate-content-body', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(langfuseParams),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error || errorData.message || `Content generation failed: ${response.statusText}`);
  }

  const result = await response.json();

  if (!result.success) {
    throw new Error(result.error || result.message || 'Content generation failed');
  }

  return result.content;
}

/**
 * NEW: Direct content generation that mirrors the server-side content generation system exactly
 * This function calls the same sb-server endpoint used by the server-side task generation
 * with the same parameters and configuration, ensuring consistent context and quality.
 */
export async function generateContentWithBatchSystem(params: StudioContentGenerationParams): Promise<string> {
  const langfuseParams = transformStudioParamsToLangfuse(params);

  // Call the same endpoint used by the batch generation system
  const response = await fetch('/api/ai/generate-content-body', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(langfuseParams),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error || errorData.message || `Content generation failed: ${response.statusText}`);
  }

  const result = await response.json();

  if (!result.success) {
    throw new Error(result.error || result.message || 'Content generation failed');
  }

  return result.content;
}