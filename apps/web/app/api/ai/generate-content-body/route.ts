import { NextRequest } from 'next/server';
import axios from 'axios';

/**
 * @name POST
 * @description Generate content body using server-side Langfuse prompt management
 */
export const POST = async (request: NextRequest) => {
  try {
    const body = await request.json();
    
    // Validate required parameters
    if (!body.taskDescription || !body.selectedCompanyContent) {
      return new Response(
        JSON.stringify({ 
          error: 'Missing required parameters: taskDescription and selectedCompanyContent are required' 
        }), 
        { status: 400 }
      );
    }

    console.log('Proxying content generation request to sb-server:', {
      taskDescription: body.taskDescription,
      channel: body.selectedCompanyContent?.channel,
      content_type: body.selectedCompanyContent?.content_type
    });

    // Call the sb-server endpoint
    const response = await axios.post(
      `${process.env.NEXT_PUBLIC_PUSH_SERVER}/generate-content-body`,
      body,
      {
        timeout: 60000, // 60 second timeout for content generation
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );

    return new Response(JSON.stringify(response.data), { 
      status: 200,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  } catch (error) {
    console.error('Error in content generation API route:', error);
    
    // Handle axios errors specifically
    if (axios.isAxiosError(error)) {
      const status = error.response?.status || 500;
      const message = error.response?.data?.message || error.message;
      
      return new Response(
        JSON.stringify({ 
          error: 'Content generation failed',
          message: message
        }), 
        { status }
      );
    }
    
    // Handle other errors
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      }), 
      { status: 500 }
    );
  }
};
