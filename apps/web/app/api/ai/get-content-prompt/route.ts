import { NextRequest } from 'next/server';
import axios from 'axios';

/**
 * @name POST
 * @description Get compiled Langfuse prompt for content generation (hybrid streaming approach)
 */
export const POST = async (request: NextRequest) => {
  try {
    const body = await request.json();

    console.log('🔍 [FRONTEND API] Received request for content prompt:', {
      taskDescription: body.taskDescription?.substring(0, 100) + '...',
      selectedCompanyContent: body.selectedCompanyContent,
      selectedPersonas: body.selectedPersonas,
      personas: body.personas?.length,
      selectedIcps: body.selectedIcps,
      icps: body.icps?.length,
      selectedResearch: body.selectedResearch,
      researchItems: body.researchItems?.length,
      selectedDocuments: body.selectedDocuments?.length,
      seoKeywords: body.seoKeywords,
      trendKeywords: body.trendKeywords,
      companyBrand: body.companyBrand ? 'Present' : 'Missing'
    });

    // Validate required parameters
    if (!body.taskDescription || !body.selectedCompanyContent) {
      console.log('❌ [FRONTEND API] Missing required parameters');
      return new Response(
        JSON.stringify({
          error: 'Missing required parameters: taskDescription and selectedCompanyContent are required'
        }),
        { status: 400 }
      );
    }

    console.log('🔍 [FRONTEND API] Calling sb-server at:', `${process.env.NEXT_PUBLIC_PUSH_SERVER}/get-content-prompt`);

    // Call the sb-server prompt endpoint
    const response = await axios.post(
      `${process.env.NEXT_PUBLIC_PUSH_SERVER}/get-content-prompt`,
      body,
      {
        timeout: 30000, // 30 second timeout for prompt compilation
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );

    console.log('🔍 [FRONTEND API] Received response from sb-server:', {
      status: response.status,
      hasCompiledPrompt: !!response.data.compiledPrompt,
      hasConfig: !!response.data.config,
      compiledPromptType: typeof response.data.compiledPrompt,
      compiledPromptLength: Array.isArray(response.data.compiledPrompt)
        ? response.data.compiledPrompt.length
        : 'N/A'
    });

    console.log('🔍 [FRONTEND API] Compiled prompt preview (first 300 chars):',
      JSON.stringify(response.data.compiledPrompt).substring(0, 300) + '...'
    );

    return new Response(JSON.stringify(response.data), {
      status: 200,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  } catch (error) {
    console.error('Error in get-content-prompt API route:', error);
    
    // Handle axios errors specifically
    if (axios.isAxiosError(error)) {
      const status = error.response?.status || 500;
      const message = error.response?.data?.message || error.message;
      
      return new Response(
        JSON.stringify({ 
          error: 'Failed to get content prompt',
          message: message
        }), 
        { status }
      );
    }
    
    // Handle other errors
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      }), 
      { status: 500 }
    );
  }
};
