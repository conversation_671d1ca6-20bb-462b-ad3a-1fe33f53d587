"use client";

import React, { use<PERSON><PERSON>back, useEffect, useState } from "react";

import { useParams } from "next/navigation";

import { getAIExtension } from "@blocknote/xl-ai";
import { useQuery as useZeroQuery } from "@rocicorp/zero/react";
import { Loader2 } from "lucide-react";

import { useTeamAccountWorkspace } from "@kit/team-accounts/hooks/use-team-account-workspace";
import { Button } from "@kit/ui/button";
import { Label } from "@kit/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@kit/ui/tabs";
import { Textarea } from "@kit/ui/textarea";
import { toast } from "@kit/ui/sonner";

import { SelectedDocument } from "~/components/document-selector";
import { useZero } from "~/hooks/use-zero";

import { useEditorContent } from "../../context/ContentStudioContext";
import { AdvancedOptions } from "./AdvancedOptions";

// Interface for server-side content generation parameters
interface StudioContentGenerationParams {
  taskDescription: string;
  selectedCompanyContent: any;
  selectedPersonas: string[];
  personas: any[];
  selectedIcps: string[];
  icps: any[];
  selectedResearch: string[];
  researchItems: any[];
  selectedDocuments: SelectedDocument[];
  seoKeywords: string[];
  trendKeywords: string[];
  companyBrand: any;
}

/**
 * TextContentEditor - Content Generation Component
 * 
 * IMPLEMENTED: Option 1 - Use ONLY content_editor_template field
 * 
 * This component addresses the critical architectural issue where the 
 * company_content table had TWO content fields causing context confusion:
 * 
 * ❌ content: text - Plain text content (legacy - NO LONGER USED)
 * ✅ content_editor_template: jsonb - BlockNote structured content (ONLY field used)
 * 
 * SOLUTION IMPLEMENTED: 
 * - All content operations use ONLY content_editor_template
 * - Eliminates dual field confusion entirely
 * - Ensures AI always has consistent, single source of truth for content
 * - Improves content generation quality and context understanding
 */
export const TextContentEditor: React.FC = () => {
  // Get data from context
  const params = useParams();
  const contentId = params.id;
  const { editor } = useEditorContent();
  const zero = useZero();
  const workspace = useTeamAccountWorkspace();
  const [companyContent] = useZeroQuery(
    zero.query.company_content.where('company_id', '=', workspace.account.id),
    {
      ttl: '1m',
    },
  );

  const selectedCompanyContent = companyContent.filter(
    (content: any) => content.id === contentId,
  )[0];

  const [savedResearch] = useZeroQuery(
    zero.query.saved_research.where('account_id', '=', workspace.account.id),
    {
      ttl: '1m',
    },
  );

  const [companyBrand] = useZeroQuery(
    zero.query.company_brand.where('company_id', '=', workspace.account.id),
    {
      ttl: '1m',
    },
  );

  const [personas] = useZeroQuery(
    zero.query.personas.where('company_id', '=', workspace.account.id),
    {
      ttl: '1m',
    },
  );

  const [icps] = useZeroQuery(
    zero.query.icps.where('company_id', '=', workspace.account.id),
    {
      ttl: '1m',
    },
  );

  // Add company_campaigns query
  const [companyCampaigns] = useZeroQuery(
    zero.query.company_campaigns.where('company_id', '=', workspace.account.id),
    {
      ttl: '1m',
    },
  );

  // Add product_documents query
  const [productDocuments] = useZeroQuery(
    zero.query.product_documents.where('company_id', '=', workspace.account.id),
    {
      ttl: '1m',
    },
  );

  // Local state

  // const [generatedContent, setGeneratedContent] = useState<GeneratedContent | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [taskTitle, setTaskTitle] = useState('');
  const [taskDescription, setTaskDescription] = useState('');

  // Advanced options state
  const [trendKeywords, setTrendKeywords] = useState<string[]>([]);
  const [seoKeywords, setSeoKeywords] = useState<string[]>([]);
  const [selectedDocuments, setSelectedDocuments] = useState<SelectedDocument[]>([]);
  const [selectedIcps, setSelectedIcps] = useState<string[]>([]);
  const [icpItems, setIcpItems] = useState<any[]>(icps || []);
  const [selectedPersonas, setSelectedPersonas] = useState<any[]>([]);
  const [personaItems, setPersonaItems] = useState<any[]>(personas || []);
  const [selectedResearch, setSelectedResearch] = useState<string[]>([]);
  const [researchItems, setResearchItems] = useState<any[]>(savedResearch || []);
  // Initialize task title and description from company content
  // Use refs to track if user is actively editing to prevent state conflicts
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    if (selectedCompanyContent && !isInitialized) {
      setTaskTitle(selectedCompanyContent?.task_title || '');
      setTaskDescription(selectedCompanyContent?.task_description || '');
      setIsInitialized(true);
    }
  }, [selectedCompanyContent, isInitialized]);

  // Pre-populate advanced options from campaign data
  useEffect(() => {
    if (selectedCompanyContent?.campaign_id && companyCampaigns?.length > 0) {
      const associatedCampaign = companyCampaigns.find(
        (campaign: any) => campaign.id === selectedCompanyContent.campaign_id,
      );
      if (associatedCampaign) {
        // Pre-populate target_icps
        if (
          associatedCampaign.target_icps &&
          Array.isArray(associatedCampaign.target_icps)
        ) {
          setSelectedIcps(associatedCampaign.target_icps);
        }

        // Pre-populate target_personas
        if (
          associatedCampaign.target_personas &&
          Array.isArray(associatedCampaign.target_personas)
        ) {
          setSelectedPersonas(associatedCampaign.target_personas);
        }

        // Pre-populate external_research
        if (
          associatedCampaign.external_research &&
          Array.isArray(associatedCampaign.external_research)
        ) {
          setSelectedResearch(associatedCampaign.external_research);
        }

        // Pre-populate documents - convert product IDs to SelectedDocument objects
        if (
          associatedCampaign.products &&
          Array.isArray(associatedCampaign.products) &&
          productDocuments?.length > 0
        ) {
          const selectedDocs: SelectedDocument[] = associatedCampaign.products
            .map((productId: string) => {
              const doc = productDocuments.find((pd: any) => pd.id === productId);
              return doc
                ? {
                    id: doc.id,
                    documentTitle: doc.title,
                    content: doc.content || '',
                  }
                : null;
            })
            .filter(Boolean) as SelectedDocument[];

          setSelectedDocuments(selectedDocs);
        }
      }
    }
  }, [selectedCompanyContent, companyCampaigns, productDocuments]);

  // Create a memoized function to generate content using hybrid streaming approach:
  // 1. Get compiled Langfuse prompt from server (with proper system/user message separation)
  // 2. Extract system and user messages to maintain context
  // 3. Combine them into a single, well-structured prompt for BlockNote
  // 4. Use BlockNote's getAIExtension().callLLM() for real-time streaming
  // This approach ensures the AI maintains proper context and generates relevant content
  const generateContent = useCallback(async () => {
    setIsGenerating(true);
    setHasError(false);

    if (!selectedCompanyContent?.id || !editor) {
      setIsGenerating(false);
      return;
    }

    try {
      // Prepare parameters for server-side prompt compilation
      const params: StudioContentGenerationParams = {
        taskDescription,
        selectedCompanyContent,
        selectedPersonas,
        personas,
        selectedIcps,
        icps,
        selectedResearch,
        researchItems,
        selectedDocuments,
        seoKeywords,
        trendKeywords,
        companyBrand,
      };

      console.log('🔍 [FRONTEND] Preparing to fetch compiled Langfuse prompt with params:', {
        taskDescription: taskDescription?.substring(0, 100) + '...',
        selectedCompanyContent,
        selectedPersonas,
        personas: personas?.length,
        selectedIcps,
        icps: icps?.length,
        selectedResearch,
        researchItems: researchItems?.length,
        selectedDocuments: selectedDocuments?.length,
        seoKeywords,
        trendKeywords,
        companyBrand: companyBrand ? 'Present' : 'Missing'
      });

      // Step 1: Get the compiled prompt from server (Langfuse)
      const promptResponse = await fetch('/api/ai/get-content-prompt', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(params),
      });

      if (!promptResponse.ok) {
        const errorData = await promptResponse.json();
        throw new Error(errorData.message || 'Failed to get prompt from server');
      }

      const promptResult = await promptResponse.json();
      
      // Validate the prompt result structure
      if (!promptResult.compiledPrompt) {
        throw new Error('Server did not return a compiled prompt');
      }

      // Step 2: Convert compiled messages to a single prompt string for BlockNote
      let promptString = '';
      let systemPrompt = '';
      let userPrompt = '';
      
      if (Array.isArray(promptResult.compiledPrompt)) {
        // Extract system and user messages properly
        const systemMessage = promptResult.compiledPrompt.find(msg => msg.role === 'system');
        const userMessage = promptResult.compiledPrompt.find(msg => msg.role === 'user');
        const assistantMessage = promptResult.compiledPrompt.find(msg => msg.role === 'assistant');
        
        if (systemMessage?.content) {
          systemPrompt = systemMessage.content;
        }
        
        if (userMessage?.content) {
          userPrompt = userMessage.content;
        } else if (assistantMessage?.content) {
          // If no user message, use assistant message as user prompt
          userPrompt = assistantMessage.content;
        } else {
          // Fallback: combine all messages into a single prompt
          promptString = promptResult.compiledPrompt
            .map((msg: any) => {
              if (typeof msg === 'string') return msg;
              if (msg.content) return msg.content;
              if (msg.role && msg.content) return `${msg.role}: ${msg.content}`;
              return JSON.stringify(msg);
            })
            .join('\n\n');
        }
      } else if (typeof promptResult.compiledPrompt === 'string') {
        userPrompt = promptResult.compiledPrompt;
      } else {
        promptString = JSON.stringify(promptResult.compiledPrompt);
      }

      // Step 3: Use BlockNote's AI extension with proper structured prompts
      // Ensure editor and AI extension are available
      if (!editor) {
        throw new Error('Editor is not initialized');
      }
      
      const aiExtension = getAIExtension(editor);
      if (!aiExtension) {
        throw new Error('AI extension is not available');
      }
      
      // Fix for strikethrough + new content issue:
      // Select all existing content so AI can properly replace it instead of appending
      const currentBlocks = editor.document;

      // Select all content in the editor so the AI replaces it instead of appending
      if (currentBlocks && currentBlocks.length > 0) {
        try {
          // Select from first block to last block to ensure proper replacement
          const firstBlock = currentBlocks[0];
          const lastBlock = currentBlocks[currentBlocks.length - 1];

          if (firstBlock && lastBlock) {
            editor.setSelection(firstBlock.id, lastBlock.id);
            console.log('🔍 [FRONTEND] Selected all content for replacement:', {
              firstBlockId: firstBlock.id,
              lastBlockId: lastBlock.id,
              totalBlocks: currentBlocks.length
            });
          }
        } catch (error) {
          console.warn('🔍 [FRONTEND] Could not select all content, proceeding without selection:', error);
        }
      }

      if (systemPrompt && userPrompt) {
        // Combine system and user prompts into a single, well-structured prompt
        const combinedPrompt = `${systemPrompt}\n\n${userPrompt}`;

        // Essential log: Show what we're sending to callLLM
        console.log('🔍 [FRONTEND] Sending to callLLM:', {
          systemPrompt: systemPrompt.substring(0, 200) + '...',
          userPrompt: userPrompt.substring(0, 200) + '...',
          combinedPrompt: combinedPrompt.substring(0, 300) + '...'
        });

        await aiExtension.callLLM({
          userPrompt: combinedPrompt,
          useSelection: true, // Use selection to replace the selected content
          defaultStreamTools: {
            add: true,
            update: true,
            delete: true,
          },
          deleteEmptyCursorBlock: true,
        });
      } else {
        // Fallback to single prompt approach
        const finalPrompt = promptString || userPrompt || 'Generate content based on the provided context.';

        // Essential log: Show what we're sending to callLLM
        console.log('🔍 [FRONTEND] Sending to callLLM (fallback):', {
          finalPrompt: finalPrompt.substring(0, 300) + '...'
        });

        await aiExtension.callLLM({
          userPrompt: finalPrompt,
          useSelection: true, // Use selection to replace the selected content
          defaultStreamTools: {
            add: true,
            update: true,
            delete: true,
          },
          deleteEmptyCursorBlock: true,
        });
      }

      console.log('🔍 [FRONTEND] BlockNote AI extension call completed successfully');
      setIsGenerating(false);
      
      // Show success toast to user
      toast.success('Content generated successfully!');
      
      // OPTION 1 IMPLEMENTED: Use ONLY content_editor_template field
      // This eliminates the dual field confusion by ensuring all content operations
      // use the single, structured content_editor_template field
      setTimeout(async () => {
        try {
          const currentBlocks = editor.document;
          if (currentBlocks && currentBlocks.length > 0) {
            // Update ONLY the content_editor_template field
            // @ts-expect-error - TypeScript issue with Zero mutator typing
            zero.mutate.company_content.update({
              id: selectedCompanyContent.id,
              values: {
                content_editor_template: currentBlocks, // ✅ ONLY this field
                updated_at: Date.now()
              }
            });
            
            console.log('🔍 [FRONTEND] Updated content_editor_template field only - eliminating dual field confusion');
          }
        } catch (syncError) {
          console.error('Failed to update content_editor_template field:', syncError);
        }
      }, 2000); // Wait 2 seconds for AI generation to complete
    } catch (error) {
      console.error('Error in hybrid content generation:', error);
      
      // Provide specific error messages for different failure scenarios
      let errorMessage = 'Failed to generate content';
      if (error instanceof Error) {
        if (error.message.includes('compiled prompt')) {
          errorMessage = 'Failed to compile content prompt from server';
        } else if (error.message.includes('Editor is not initialized')) {
          errorMessage = 'Content editor is not ready. Please try again.';
        } else if (error.message.includes('AI extension')) {
          errorMessage = 'AI extension is not available. Please refresh the page.';
        } else {
          errorMessage = error.message;
        }
      }
      
      setHasError(true);
      setIsGenerating(false);
      
      // Show error toast to user
      toast.error(errorMessage);
    }
  }, [
    selectedCompanyContent,
    editor,
    taskDescription,
    selectedPersonas,
    personas,
    selectedIcps,
    icps,
    selectedResearch,
    researchItems,
    selectedDocuments,
    seoKeywords,
    trendKeywords,
    companyBrand,
  ]);

  const onDescriptionChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setTaskDescription(e.target.value);
    // Update Zero Sync Engine with the new task description
    // @ts-expect-error - TypeScript issue with Zero mutator typing
    zero.mutate.company_content.update({
      id: selectedCompanyContent?.id || '',
      values: {
        task_description: e.target.value,
      },
    });
  };

  return (
    <div className="space-y-4 p-4">
      {/* <div className="space-y-2">
        <Label className="text-lg font-semibold">Task Title</Label>
        <Textarea 
          value={taskTitle} 
          onChange={(e) => setTaskTitle(e.target.value)}
          className="text-lg"
          rows={2}
          disabled={isGenerating}
        />
      </div> */}

      <Tabs defaultValue="basic" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="basic">Basic</TabsTrigger>
          <TabsTrigger value="advanced">Advanced</TabsTrigger>
        </TabsList>

        <TabsContent value="basic" className="space-y-4">
          <div className="space-y-2">
            <Label className="text-lg font-semibold">Topic</Label>
            <br />
            <Label className="text-muted-foreground text-sm">
              Enter the topic or basis used to generate the content.
            </Label>

            <Textarea
              value={taskDescription}
              onChange={onDescriptionChange}
              className="text-muted-foreground"
              rows={4}
              disabled={isGenerating}
            />
          </div>
        </TabsContent>

        <TabsContent value="advanced" className="space-y-4">
          <div className="space-y-2">
            <Label className="text-lg font-semibold">Topic</Label>
            <br />
            <Label className="text-muted-foreground text-sm">
              Enter the topic or basis used to generate the content.
            </Label>
            <Textarea
              value={taskDescription}
              onChange={onDescriptionChange}
              className="text-muted-foreground"
              rows={4}
              disabled={isGenerating}
            />
          </div>
          <AdvancedOptions
            selectedDocuments={selectedDocuments}
            onDocumentsChange={setSelectedDocuments}
            selectedIcps={selectedIcps}
            onIcpsChange={setSelectedIcps}
            icps={icpItems}
            onIcpsListChange={setIcpItems}
            selectedPersonas={selectedPersonas}
            onPersonasChange={setSelectedPersonas}
            personas={personaItems}
            onPersonasListChange={setPersonaItems}
            trendKeywords={trendKeywords}
            onTrendKeywordsChange={setTrendKeywords}
            seoKeywords={seoKeywords}
            onSeoKeywordsChange={setSeoKeywords}
            selectedResearch={selectedResearch}
            onResearchChange={setSelectedResearch}
            researchItems={researchItems}
            onResearchItemsChange={setResearchItems}
          />
        </TabsContent>
      </Tabs>

      <Button
        onClick={generateContent}
        disabled={isGenerating || !taskDescription.trim()}
      >
        {isGenerating ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Generating...
          </>
        ) : (
          'Generate'
        )}
      </Button>

      {hasError && (
        <div className="mt-2">
          <button
            onClick={generateContent}
            className="cursor-pointer text-sm text-red-600 underline hover:text-red-800"
          >
            Error Generating, Click to try again
          </button>
        </div>
      )}

      {/* {generatedContent && (
        <ScrollArea className="h-[calc(100vh-200px)]">
          <div className="space-y-6">
            <Accordion type="single" collapsible className="w-full">
              <AccordionItem value="keywords">
                <AccordionTrigger className="px-4 py-2 hover:no-underline">
                  <Label className="text-lg font-semibold">Keywords</Label>
                </AccordionTrigger>
                <AccordionContent className="px-4 pb-4">
                  <div className="space-y-4">
                    <div>
                      <Label className="text-sm">SEO Keywords</Label>
                      <div className="mt-2 flex flex-wrap gap-2">
                        {generatedContent.seo_keywords_used.map((keyword, index) => (
                          <Badge key={index} variant="secondary">{keyword}</Badge>
                        ))}
                      </div>
                    </div>
                    {generatedContent.trend_keywords_used.length > 0 && (
                      <div>
                        <Separator className="my-4" />
                        <Label className="text-sm">Trend Keywords</Label>
                        <div className="mt-2 flex flex-wrap gap-2">
                          {generatedContent.trend_keywords_used.map((keyword, index) => (
                            <Badge key={index} variant="outline">{keyword}</Badge>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </AccordionContent>
              </AccordionItem>
              
              <AccordionItem value="rationale">
                <AccordionTrigger className="px-4 py-2 hover:no-underline">
                  <Label className="text-lg font-semibold">Creative Rationale</Label>
                </AccordionTrigger>
                <AccordionContent className="px-4 pb-4">
                  <p>{generatedContent.rationale_for_creative_choices}</p>
                </AccordionContent>
              </AccordionItem>
            </Accordion>

          </div>
        </ScrollArea>
      )} */}
    </div>
  );
};
