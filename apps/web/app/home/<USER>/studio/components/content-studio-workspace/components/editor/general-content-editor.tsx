'use client';
import { Block } from "@blocknote/core";
import "@blocknote/core/fonts/inter.css";
import { BlockNoteView } from "@blocknote/mantine";
import "@blocknote/mantine/style.css";
import { useEffect, useState, useCallback, useMemo } from "react";
import { updateCompanyContent } from "~/services/company-content";
import { useQuery as useZeroQuery } from '@rocicorp/zero/react';
import { useEditorContent } from "../../context/ContentStudioContext";
import { useTheme } from "next-themes";
import { en as aiEn } from "@blocknote/xl-ai/locales";
import "@blocknote/xl-ai/style.css"; // add the AI stylesheet
import { createOpenAI } from '@ai-sdk/openai';
import { en } from "@blocknote/core/locales";
import {
  FormattingToolbar,
  FormattingToolbarController,
  getFormattingToolbarItems,
  useCreateBlockNote,
} from "@blocknote/react";
import {
  <PERSON><PERSON>enuController,
  AIToolbarButton,
  create<PERSON>IExtension,
  createBlockNoteAIClient,
} from "@blocknote/xl-ai";
import { useZero } from "~/hooks/use-zero";
import { CompanyContent } from "~/types/company-content";
import { defaultContent } from "./default-content";
import { debounce } from "lodash";

/**
 * GeneralContentEditor - BlockNote Editor Component
 * 
 * IMPLEMENTED: Option 1 - Use ONLY content_editor_template field
 * 
 * This component ensures consistency with the TextContentEditor by using
 * only the content_editor_template field, eliminating dual field confusion.
 */
export default function GeneralContentEditor({
  companyContent,
  editable = true
}: {
  companyContent: CompanyContent,
  editable?: boolean
}) {
  // Track if this is the initial content load
  console.log("companyContent", companyContent);
  const { theme } = useTheme();
  const zero = useZero();

  // Use Zero Sync Engine to reactively fetch the latest content data
  const [latestCompanyContent] = useZeroQuery(
    zero.query.company_content.where("id", "=", companyContent.id),
    {
      ttl: '1m'
    }
  );

  // Use the latest data from Zero Sync Engine if available, otherwise fall back to prop
  const currentContent = latestCompanyContent?.[0] || companyContent;


  const client = createBlockNoteAIClient({
    apiKey: 'my-secret-token',
    baseURL: '/api/ai/stream-content',
  });

  const model = createOpenAI({
    ...client.getProviderSettings("google")
  })("gemini-2.5-pro")

  const editor = useCreateBlockNote({
    // Register the AI extension
    dictionary: {
      ...en,
      ai: aiEn, // add default translations for the AI extension
    },
    extensions: [
      createAIExtension({
        model
      }),
    ],
  });

  const { setEditor } = useEditorContent();

  // Memoize default content to prevent unnecessary re-creation
  const memoizedDefaultContent = useMemo(() => defaultContent, [defaultContent]);

  // Create debounced update function (saves every 5 seconds)
  const debouncedUpdateContent = useCallback(
    debounce((blocks: Block[]) => {
      // @ts-expect-error - Zero mutator typing issue
      zero.mutate.company_content.update({
        id: currentContent.id,
        values: {
          content_editor_template: blocks,
        }
      });
    }, 3000),
    [currentContent.id, zero.mutate.company_content]
  );

  // Handle initial content setup
  useEffect(() => {
    if (!editor) return;
    
    // Only set editor once when it's first created
    setEditor(editor);
  }, [editor, setEditor]);

  // Handle content updates separately to prevent constant re-rendering
  useEffect(() => {
    if (!editor) return;

    async function updateContent() {
      let blocks: Block[];

      // OPTION 1 IMPLEMENTED: Use ONLY content_editor_template field
      // Priority order:
      // 1. Use content_editor_template if it exists (structured blocks) ✅
      // 2. Fall back to default content (no more content field fallback)
      if (currentContent.content_editor_template && Array.isArray(currentContent.content_editor_template)) {
        blocks = currentContent.content_editor_template as Block[];
      } else {
        // No fallback to content field - use default content
        blocks = memoizedDefaultContent as Block[];
      }

      // Only update if the content is actually different
      const currentBlocks = editor.document;
      const hasChanged = JSON.stringify(currentBlocks) !== JSON.stringify(blocks);
      
      if (hasChanged) {
        editor.replaceBlocks(editor.document, blocks);
      }
    }

    updateContent();
  }, [currentContent.content_editor_template, editor, memoizedDefaultContent]); // Removed setEditor dependency

  // Cleanup debounced function on unmount
  useEffect(() => {
    return () => {
      debouncedUpdateContent.cancel();
    };
  }, [debouncedUpdateContent]);

  return (
    <>
    <BlockNoteView
      editor={editor} 
      editable={editable}
      theme={theme as "light" | "dark"} 
      formattingToolbar={true}
      onChange={() => {
        const blocks = editor.document;
        debouncedUpdateContent(blocks);
      }}
    >
       <AIMenuController />
       <FormattingToolbarWithAI />
    </BlockNoteView>
    </>
  );
}

function FormattingToolbarWithAI() {
  return (
    <FormattingToolbarController
      formattingToolbar={() => (
        <FormattingToolbar>
          {...getFormattingToolbarItems()}
          {/* Add the AI button */}
          <AIToolbarButton />
        </FormattingToolbar>
      )}
    />
  );
}
 